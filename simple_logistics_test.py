#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试 - 查找已取消订单中的物流单号
"""

import logging
from datetime import datetime, timedelta
from get_canceled_sales import setup_logging, get_today_time_range
from wdt_post_client import WDTPostClient

def main():
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        # 获取当天时间范围
        start_time, end_time = get_today_time_range()
        logger.info(f"查询时间范围: {start_time} 至 {end_time}")
        
        # 查询已取消订单
        logger.info("查询已取消订单...")
        result = client.query_sales_trades(
            start_time=start_time,
            end_time=end_time,
            trade_status=5,
            page_no=0,
            page_size=50
        )
        
        if isinstance(result, dict) and result.get('flag') == 'success':
            trades = result.get('content', [])
            total = result.get('total', 0)
            
            logger.info(f"查询到 {len(trades)} 条已取消订单，总计 {total} 条")
            
            # 分析每条订单
            logistics_count = 0
            for i, trade in enumerate(trades, 1):
                trade_no = trade.get('trade_no', '未知')
                logistics_no = trade.get('logistics_no', '')
                
                # 显示前10条的详细信息
                if i <= 10:
                    logger.info(f"订单 {i}: {trade_no}")
                    logger.info(f"  物流单号: '{logistics_no}'")
                    logger.info(f"  物流单号长度: {len(logistics_no) if logistics_no else 0}")
                    logger.info(f"  物流单号类型: {type(logistics_no)}")
                    
                    # 检查所有可能的物流字段
                    logistics_fields = ['logistics_no', 'logistics_code', 'logistics_name', 'express_no']
                    for field in logistics_fields:
                        value = trade.get(field, '')
                        if value:
                            logger.info(f"  {field}: '{value}'")
                
                # 统计有物流单号的订单
                if logistics_no and str(logistics_no).strip():
                    logistics_count += 1
                    logger.info(f"✓ 发现物流单号: 订单{trade_no}, 物流单号'{logistics_no}'")
            
            logger.info(f"\n总结: 在 {len(trades)} 条已取消订单中，发现 {logistics_count} 条包含物流单号")
            
            # 如果没有找到，尝试查看订单的完整字段
            if logistics_count == 0 and trades:
                logger.info("\n=== 第一条订单的所有字段 ===")
                first_trade = trades[0]
                for key, value in first_trade.items():
                    logger.info(f"{key}: {value}")
        
        else:
            logger.error("API调用失败")
            
    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)

if __name__ == '__main__':
    main()

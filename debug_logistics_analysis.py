#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
深度分析物流单号问题 - 查找已取消订单中包含物流单号的情况
"""

import logging
from datetime import datetime, timedelta
from get_canceled_sales import setup_logging, get_today_time_range
from wdt_post_client import WDTPostClient
import json

def analyze_canceled_orders_with_logistics():
    """分析已取消订单中是否存在物流单号"""
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        # 获取当天时间范围
        start_time, end_time = get_today_time_range()
        logger.info(f"查询时间范围: {start_time} 至 {end_time}")
        
        # 查询已取消订单（状态5）
        logger.info(f"\n=== 分析已取消订单（状态5）===")
        result = client.query_sales_trades(
            start_time=start_time,
            end_time=end_time,
            trade_status=5,
            page_no=0,
            page_size=100  # 查询更多数据
        )
        
        if isinstance(result, dict) and result.get('flag') == 'success':
            trades = []
            if 'content' in result:
                content = result.get('content', [])
                if isinstance(content, list):
                    trades = content
            
            total = result.get('total', 0)
            logger.info(f"查询到 {len(trades)} 条已取消订单，总计 {total} 条")
            
            # 分析每条订单的详细信息
            logistics_found = 0
            for i, trade in enumerate(trades, 1):
                trade_no = trade.get('trade_no', '未知')
                logistics_no = trade.get('logistics_no', '')
                
                # 检查所有可能的物流相关字段
                logistics_fields = [
                    'logistics_no', 'logistics_code', 'logistics_name', 
                    'logistics_company', 'express_no', 'tracking_no',
                    'waybill_no', 'delivery_no'
                ]
                
                logistics_info = {}
                for field in logistics_fields:
                    value = trade.get(field, '')
                    if value and str(value).strip():
                        logistics_info[field] = value
                
                if logistics_info:
                    logistics_found += 1
                    logger.info(f"  订单 {i}: {trade_no} - 发现物流信息: {logistics_info}")
                
                # 显示前10条的详细字段
                if i <= 10:
                    logger.info(f"\n  === 订单 {i} 详细信息 ===")
                    logger.info(f"  订单号: {trade_no}")
                    logger.info(f"  状态: {trade.get('trade_status', '未知')}")
                    logger.info(f"  状态名称: {trade.get('trade_status_name', '未知')}")
                    logger.info(f"  下单时间: {trade.get('trade_time', '未知')}")
                    logger.info(f"  修改时间: {trade.get('modified', '未知')}")
                    
                    # 显示所有物流相关字段
                    for field in logistics_fields:
                        value = trade.get(field, '')
                        logger.info(f"  {field}: {value}")
            
            logger.info(f"\n总结: 在 {len(trades)} 条已取消订单中，发现 {logistics_found} 条包含物流信息")
            
        return True
        
    except Exception as e:
        logger.error(f"分析失败: {e}", exc_info=True)
        return False

def analyze_different_time_periods():
    """分析不同时间段的订单，寻找包含物流单号的已取消订单"""
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        # 测试过去几天的数据
        now = datetime.now()
        
        time_periods = [
            ("今天上午", now.replace(hour=0, minute=0, second=0), now.replace(hour=12, minute=0, second=0)),
            ("今天下午", now.replace(hour=12, minute=0, second=0), now),
            ("昨天", now - timedelta(days=1), now - timedelta(days=1) + timedelta(hours=23, minutes=59, seconds=59)),
            ("前天", now - timedelta(days=2), now - timedelta(days=2) + timedelta(hours=23, minutes=59, seconds=59)),
        ]
        
        total_logistics_found = 0
        
        for period_name, start_time, end_time in time_periods:
            logger.info(f"\n=== 分析时间段: {period_name} ===")
            logger.info(f"时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} 至 {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            try:
                result = client.query_sales_trades(
                    start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                    end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                    trade_status=5,
                    page_no=0,
                    page_size=50
                )
                
                if isinstance(result, dict) and result.get('flag') == 'success':
                    trades = []
                    if 'content' in result:
                        content = result.get('content', [])
                        if isinstance(content, list):
                            trades = content
                    
                    total = result.get('total', 0)
                    logger.info(f"查询到 {len(trades)} 条记录，总计 {total} 条")
                    
                    # 查找包含物流单号的订单
                    period_logistics_found = 0
                    for trade in trades:
                        logistics_no = trade.get('logistics_no', '')
                        if logistics_no and logistics_no.strip():
                            period_logistics_found += 1
                            total_logistics_found += 1
                            trade_no = trade.get('trade_no', '未知')
                            trade_time = trade.get('trade_time', '未知')
                            logger.info(f"  ✓ 发现物流单号: 订单{trade_no}, 物流单号{logistics_no}, 下单时间{trade_time}")
                    
                    logger.info(f"  {period_name}: 发现 {period_logistics_found} 条包含物流单号的已取消订单")
                else:
                    logger.warning(f"{period_name}: API调用失败")
                    
            except Exception as e:
                logger.error(f"{period_name} 分析失败: {e}")
        
        logger.info(f"\n=== 总结 ===")
        logger.info(f"在所有时间段中，总共发现 {total_logistics_found} 条包含物流单号的已取消订单")
        
        return True
        
    except Exception as e:
        logger.error(f"时间段分析失败: {e}", exc_info=True)
        return False

def analyze_order_lifecycle():
    """分析订单生命周期 - 查看订单从创建到取消的过程"""
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        # 获取当天时间范围
        start_time, end_time = get_today_time_range()
        
        logger.info(f"\n=== 分析订单生命周期 ===")
        
        # 查询待审核订单（状态30）
        logger.info(f"1. 查询待审核订单（状态30）...")
        result_pending = client.query_sales_trades(
            start_time=start_time,
            end_time=end_time,
            trade_status=30,
            page_no=0,
            page_size=10
        )
        
        if isinstance(result_pending, dict) and result_pending.get('flag') == 'success':
            pending_trades = result_pending.get('content', [])
            logger.info(f"待审核订单: {len(pending_trades)} 条")
            
            for i, trade in enumerate(pending_trades[:3], 1):
                trade_no = trade.get('trade_no', '未知')
                logistics_no = trade.get('logistics_no', '')
                logger.info(f"  待审核订单{i}: {trade_no}, 物流单号: {logistics_no if logistics_no else '无'}")
        
        # 查询已取消订单（状态5）
        logger.info(f"\n2. 查询已取消订单（状态5）...")
        result_canceled = client.query_sales_trades(
            start_time=start_time,
            end_time=end_time,
            trade_status=5,
            page_no=0,
            page_size=10
        )
        
        if isinstance(result_canceled, dict) and result_canceled.get('flag') == 'success':
            canceled_trades = result_canceled.get('content', [])
            logger.info(f"已取消订单: {len(canceled_trades)} 条")
            
            for i, trade in enumerate(canceled_trades[:3], 1):
                trade_no = trade.get('trade_no', '未知')
                logistics_no = trade.get('logistics_no', '')
                trade_time = trade.get('trade_time', '未知')
                modified = trade.get('modified', '未知')
                logger.info(f"  已取消订单{i}: {trade_no}")
                logger.info(f"    下单时间: {trade_time}")
                logger.info(f"    修改时间: {modified}")
                logger.info(f"    物流单号: {logistics_no if logistics_no else '无'}")
        
        return True
        
    except Exception as e:
        logger.error(f"订单生命周期分析失败: {e}", exc_info=True)
        return False

def main():
    """主分析函数"""
    logger = setup_logging()
    
    logger.info("=== 开始深度分析物流单号问题 ===")
    
    # 分析1: 详细分析已取消订单
    logger.info("\n1. 详细分析已取消订单...")
    if analyze_canceled_orders_with_logistics():
        logger.info("✅ 已取消订单分析完成")
    else:
        logger.error("❌ 已取消订单分析失败")
    
    # 分析2: 不同时间段分析
    logger.info("\n2. 不同时间段分析...")
    if analyze_different_time_periods():
        logger.info("✅ 时间段分析完成")
    else:
        logger.error("❌ 时间段分析失败")
    
    # 分析3: 订单生命周期分析
    logger.info("\n3. 订单生命周期分析...")
    if analyze_order_lifecycle():
        logger.info("✅ 订单生命周期分析完成")
    else:
        logger.error("❌ 订单生命周期分析失败")
    
    logger.info("\n=== 深度分析完成 ===")
    logger.info("请查看日志输出，寻找包含物流单号的已取消订单模式")
    
    return 0

if __name__ == '__main__':
    exit(main())

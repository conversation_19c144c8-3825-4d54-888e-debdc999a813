#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试出库单状态码，找出正确的已取消状态
"""

import logging
from datetime import datetime, timedelta
from get_canceled_sales import setup_logging, get_today_time_range
from wdt_post_client import WDTPostClient

def test_different_status_codes():
    """测试不同的状态码，找出已取消状态的正确值"""
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        # 获取当天时间范围
        start_time, end_time = get_today_time_range()
        logger.info(f"查询时间范围: {start_time} 至 {end_time}")
        
        # 测试不同的状态码
        status_codes_to_test = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
        
        for status_code in status_codes_to_test:
            try:
                logger.info(f"\n=== 测试状态码 {status_code} ===")
                
                result = client.query_stockouts(
                    start_consign_time=start_time,
                    end_consign_time=end_time,
                    status=status_code,
                    page_no=0,
                    page_size=5  # 只查询5条测试
                )
                
                if isinstance(result, dict) and result.get('flag') == 'success':
                    # 解析数据
                    stockouts = []
                    if 'content' in result:
                        content = result.get('content', [])
                        if isinstance(content, list):
                            stockouts = content
                    
                    count = len(stockouts)
                    total = result.get('total', 0)
                    
                    logger.info(f"状态码 {status_code}: 查询到 {count} 条记录，总计 {total} 条")
                    
                    if count > 0:
                        # 显示第一条记录的详细信息
                        first_stockout = stockouts[0]
                        stockout_no = first_stockout.get('stockout_no', '未知')
                        status_name = first_stockout.get('status_name', '未知')
                        logistics_no = first_stockout.get('logistics_no', '')
                        consign_time = first_stockout.get('consign_time', '')
                        modified = first_stockout.get('modified', '')
                        
                        logger.info(f"  示例记录:")
                        logger.info(f"    出库单号: {stockout_no}")
                        logger.info(f"    状态名称: {status_name}")
                        logger.info(f"    物流单号: {logistics_no}")
                        logger.info(f"    发货时间: {consign_time}")
                        logger.info(f"    修改时间: {modified}")
                        
                        # 显示所有字段名
                        logger.debug(f"    所有字段: {list(first_stockout.keys())}")
                else:
                    logger.warning(f"状态码 {status_code}: API调用失败或无数据")
                    
            except Exception as e:
                logger.error(f"状态码 {status_code} 测试失败: {e}")
                
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)
        return False

def test_no_status_filter():
    """测试不使用状态过滤，查看所有出库单的状态"""
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        # 获取当天时间范围
        start_time, end_time = get_today_time_range()
        
        logger.info(f"\n=== 测试不使用状态过滤 ===")
        
        result = client.query_stockouts(
            start_consign_time=start_time,
            end_consign_time=end_time,
            # 不设置status参数
            page_no=0,
            page_size=20  # 查询20条
        )
        
        if isinstance(result, dict) and result.get('flag') == 'success':
            # 解析数据
            stockouts = []
            if 'content' in result:
                content = result.get('content', [])
                if isinstance(content, list):
                    stockouts = content
            
            count = len(stockouts)
            total = result.get('total', 0)
            
            logger.info(f"不使用状态过滤: 查询到 {count} 条记录，总计 {total} 条")
            
            # 统计不同状态的出库单
            status_count = {}
            for stockout in stockouts:
                status = stockout.get('status', '未知')
                status_name = stockout.get('status_name', '未知')
                status_key = f"{status}({status_name})"
                
                if status_key not in status_count:
                    status_count[status_key] = 0
                status_count[status_key] += 1
            
            logger.info(f"状态统计:")
            for status_key, count in status_count.items():
                logger.info(f"  {status_key}: {count} 条")
            
            # 显示前几条记录的详细信息
            logger.info(f"\n前5条记录详情:")
            for i, stockout in enumerate(stockouts[:5], 1):
                stockout_no = stockout.get('stockout_no', '未知')
                status = stockout.get('status', '未知')
                status_name = stockout.get('status_name', '未知')
                logistics_no = stockout.get('logistics_no', '')
                consign_time = stockout.get('consign_time', '')
                modified = stockout.get('modified', '')
                
                logger.info(f"  {i}. 出库单号: {stockout_no}")
                logger.info(f"     状态: {status}({status_name})")
                logger.info(f"     物流单号: {logistics_no}")
                logger.info(f"     发货时间: {consign_time}")
                logger.info(f"     修改时间: {modified}")
                logger.info("")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)
        return False

def main():
    """主测试函数"""
    logger = setup_logging()
    
    logger.info("=== 开始调试出库单状态码 ===")
    
    # 测试1: 不使用状态过滤，查看所有状态
    logger.info("\n1. 测试不使用状态过滤...")
    if test_no_status_filter():
        logger.info("✅ 无状态过滤测试完成")
    else:
        logger.error("❌ 无状态过滤测试失败")
    
    # 测试2: 测试不同状态码
    logger.info("\n2. 测试不同状态码...")
    if test_different_status_codes():
        logger.info("✅ 状态码测试完成")
    else:
        logger.error("❌ 状态码测试失败")
    
    logger.info("\n=== 调试完成 ===")
    return 0

if __name__ == '__main__':
    exit(main())

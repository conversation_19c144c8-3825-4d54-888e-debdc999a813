# 旺店通已取消销售订单查询工具

## 功能概述

本工具使用旺店通 `sales.trade.query` 接口，获取当天已取消的销售订单明细。

## ⚠️ 重要说明

**已修复BUG**：之前错误地使用了 `stockout.query` 接口查询出库单，但出库单都是已发货的订单。现在正确使用 `sales.trade.query` 接口查询已取消的销售订单。

## 主要功能

### 1. 查询已取消销售订单
- 使用 `sales.trade.query` 接口查询当天已取消的销售订单
- 状态码5表示已取消状态
- 自动分页查询，获取所有符合条件的数据

### 2. 物流单号识别
- 自动识别包含物流单号的订单（通常已取消订单没有物流单号）
- 统计有物流单号和无物流单号的订单数量
- 在日志中明确标识是否包含物流单号（✓/✗）

### 3. 数据导出
- 支持导出所有已取消销售订单到Excel
- 支持仅导出包含物流单号的销售订单到Excel
- Excel文件包含详细的订单信息和物流单号标识

## 文件说明

### 核心文件
- `get_canceled_sales.py` - 主程序，查询和处理已取消销售出库单
- `wdt_post_client.py` - 旺店通API客户端，实现 `stockout.query` 接口调用
- `config.py` - 配置文件，包含API连接参数
- `.env` - 环境变量配置文件

### 测试文件
- `test_canceled_sales.py` - 实际API测试脚本
- `test_mock_canceled_sales.py` - 模拟测试脚本，验证代码逻辑

## 使用方法

### 1. 配置环境
确保 `.env` 文件包含正确的旺店通API配置：
```
WDT_SID=your_sid
WDT_APP_KEY=your_app_key
WDT_APP_SECRET=your_app_secret
WDT_API_URL=https://openapi.wdtwms.com/open_api/service.php
```

### 2. 安装依赖
```bash
pip install requests python-dotenv openpyxl
```

### 3. 运行程序
```bash
python get_canceled_sales.py
```

### 4. 查看结果
程序会显示：
- 总计已取消出库单数量
- 包含物流单号的出库单数量
- 无物流单号的出库单数量
- 每个出库单的详细信息

### 5. 导出选项
程序提供三个导出选项：
1. 导出所有已取消出库单
2. 仅导出包含物流单号的出库单
3. 不导出

## API接口详情

### sales.trade.query 接口参数
- `start_time`: 开始时间
- `end_time`: 结束时间
- `trade_status`: 订单状态（5=已取消，30=待审核）
- `page_no`: 页号（从0开始）
- `page_size`: 分页大小（最大100）

### 返回数据字段
主要关注的字段：
- `trade_no`: 订单号
- `logistics_no`: 物流单号 ⭐（通常已取消订单没有）
- `shop_name`: 店铺名称
- `receiver_name`: 收件人
- `receiver_mobile`: 收件人电话
- `trade_time`: 下单时间
- `modified`: 修改时间（取消时间）
- `trade_status`: 状态（5=已取消）

## 输出示例

### 控制台输出
```
开始查询2025-07-22 00:00:00至2025-07-22 15:10:34期间已取消的销售订单明细（包含物流单号）...
第1页查询到10条已取消销售订单:
  1. 订单号: LL202507220127
     时间: 2025-07-22 06:20:08
     店铺: DY-七彩虹抖音
     物流单号: 无

  2. 订单号: LL202507220478
     时间: 2025-07-22 10:59:14
     店铺: DY-抖音供应链管理平台
     物流单号: 无

  3. 订单号: LL202507220655
     时间: 2025-07-22 12:47:01
     店铺: DY-抖音供应链管理平台
     物流单号: 无

共查询到10条已取消的销售订单
其中包含物流单号的有0条

=== 查询结果汇总 ===
总计已取消销售订单: 10 条
包含物流单号的: 0 条
无物流单号的: 10 条
```

### Excel导出
生成的Excel文件包含以下列：
- 序号
- 订单号
- 物流单号
- 店铺名称
- 下单时间
- 创建时间
- 修改时间
- 取消时间
- 状态
- 收件人
- 收件人电话
- 收件人地址
- 备注
- 是否有物流单号

## 注意事项

1. **时间范围**: 程序查询当天（00:00:00到当前时间）的已取消销售订单
2. **物流单号**: 已取消的订单通常没有物流单号（因为没有发货）
3. **分页查询**: 自动处理分页，确保获取所有数据
4. **错误处理**: 包含完整的错误处理和日志记录
5. **API限制**: 遵循旺店通API的调用频率限制，时间跨度不能超过1天
6. **状态码限制**: sales.trade.query 接口只支持状态码5（已取消）和30（待审核）

## 测试验证

运行模拟测试验证代码逻辑：
```bash
python test_mock_canceled_sales.py
```

测试结果显示：
- ✅ 查询功能测试通过
- ✅ Excel导出功能测试通过
- ✅ API调用参数验证通过

## 更新日志

### 2025-07-22 v2.0 - 重大修复
- **🔧 修复重大BUG**: 从错误的 `stockout.query` 接口改为正确的 `sales.trade.query` 接口
- **✅ 正确查询已取消订单**: 现在能正确获取状态码为5的已取消销售订单
- **📊 更新数据结构**: 适配销售订单数据结构，而非出库单数据结构
- **🎯 明确功能定位**: 查询已取消的销售订单，而非已发货的出库单
- **📝 完善文档**: 更新README说明正确的接口和功能

### 2025-07-22 v1.0 - 初始版本（已废弃）
- ❌ 错误使用 `stockout.query` 接口（查询的是已发货出库单）
- 增加物流单号识别和统计功能
- 增强Excel导出功能，添加物流单号标识列
- 添加模拟测试验证代码逻辑
- 完善日志输出和错误处理

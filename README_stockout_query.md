# 旺店通已取消销售出库单查询工具

## 功能概述

本工具使用旺店通 `stockout.query` 接口，获取当天已取消的销售出库明细，特别关注包含物流单号的出库单。

## 主要功能

### 1. 查询已取消销售出库单
- 使用 `stockout.query` 接口查询当天已取消的销售出库单
- 支持按修改时间范围查询，确保获取当天取消的单据
- 自动分页查询，获取所有符合条件的数据

### 2. 物流单号识别
- 自动识别包含物流单号的出库单
- 统计有物流单号和无物流单号的出库单数量
- 在日志中明确标识是否包含物流单号（✓/✗）

### 3. 数据导出
- 支持导出所有已取消出库单到Excel
- 支持仅导出包含物流单号的出库单到Excel
- Excel文件包含详细的出库单信息和物流单号标识

## 文件说明

### 核心文件
- `get_canceled_sales.py` - 主程序，查询和处理已取消销售出库单
- `wdt_post_client.py` - 旺店通API客户端，实现 `stockout.query` 接口调用
- `config.py` - 配置文件，包含API连接参数
- `.env` - 环境变量配置文件

### 测试文件
- `test_canceled_sales.py` - 实际API测试脚本
- `test_mock_canceled_sales.py` - 模拟测试脚本，验证代码逻辑

## 使用方法

### 1. 配置环境
确保 `.env` 文件包含正确的旺店通API配置：
```
WDT_SID=your_sid
WDT_APP_KEY=your_app_key
WDT_APP_SECRET=your_app_secret
WDT_API_URL=https://openapi.wdtwms.com/open_api/service.php
```

### 2. 安装依赖
```bash
pip install requests python-dotenv openpyxl
```

### 3. 运行程序
```bash
python get_canceled_sales.py
```

### 4. 查看结果
程序会显示：
- 总计已取消出库单数量
- 包含物流单号的出库单数量
- 无物流单号的出库单数量
- 每个出库单的详细信息

### 5. 导出选项
程序提供三个导出选项：
1. 导出所有已取消出库单
2. 仅导出包含物流单号的出库单
3. 不导出

## API接口详情

### stockout.query 接口参数
- `start_consign_time`: 开始发货时间
- `end_consign_time`: 结束发货时间
- `start_modified`: 开始修改时间（用于查询取消时间）
- `end_modified`: 结束修改时间（用于查询取消时间）
- `status`: 出库单状态（5=已取消）
- `page_no`: 页号（从0开始）
- `page_size`: 分页大小（最大100）

### 返回数据字段
主要关注的字段：
- `stockout_no`: 出库单号
- `trade_no`: 订单号
- `logistics_no`: 物流单号 ⭐
- `shop_name`: 店铺名称
- `receiver_name`: 收件人
- `receiver_mobile`: 收件人电话
- `consign_time`: 发货时间
- `modified`: 修改时间（取消时间）
- `status`: 状态

## 输出示例

### 控制台输出
```
开始查询2025-07-22 00:00:00至2025-07-22 14:38:51期间已取消的销售出库单明细（包含物流单号）...
第1页查询到3条已取消出库单:
  1. 出库单号: SO2025072201
     时间: 2025-07-22 11:00:00
     订单号: T2025072201
     收件人: 张三
     店铺: 测试店铺1
     物流单号: SF1234567890 ✓

  2. 出库单号: SO2025072202
     时间: 2025-07-22 14:30:00
     订单号: T2025072202
     收件人: 李四
     店铺: 测试店铺2
     物流单号: 无

共查询到3条已取消的销售出库单
其中包含物流单号的有2条

=== 查询结果汇总 ===
总计已取消出库单: 3 条
包含物流单号的: 2 条
无物流单号的: 1 条
```

### Excel导出
生成的Excel文件包含以下列：
- 序号
- 出库单号
- 订单号
- 物流单号
- 店铺名称
- 发货时间
- 创建时间
- 修改时间
- 取消时间
- 状态
- 收件人
- 收件人电话
- 收件人地址
- 备注
- 是否有物流单号

## 注意事项

1. **时间范围**: 程序查询当天（00:00:00到当前时间）的已取消出库单
2. **物流单号**: 只有非空且非空白的物流单号才被认为是有效的
3. **分页查询**: 自动处理分页，确保获取所有数据
4. **错误处理**: 包含完整的错误处理和日志记录
5. **API限制**: 遵循旺店通API的调用频率限制

## 测试验证

运行模拟测试验证代码逻辑：
```bash
python test_mock_canceled_sales.py
```

测试结果显示：
- ✅ 查询功能测试通过
- ✅ Excel导出功能测试通过
- ✅ API调用参数验证通过

## 更新日志

### 2025-07-22
- 修改为使用 `stockout.query` 接口
- 增加物流单号识别和统计功能
- 优化查询参数，使用修改时间范围
- 增强Excel导出功能，添加物流单号标识列
- 添加模拟测试验证代码逻辑
- 完善日志输出和错误处理

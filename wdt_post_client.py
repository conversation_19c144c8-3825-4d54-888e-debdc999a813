#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
旺店通WMS API客户端 - POST请求版本
根据官方接口调用规范实现
"""

import hashlib
import json
import logging
import requests
import time
from typing import Dict, Any, Optional

from config import WDTConfig

class WDTPostAPIException(Exception):
    """旺店通POST API异常"""
    pass

class WDTPostSignature:
    """旺店通POST API签名工具"""
    
    @staticmethod
    def build_request_params(
        method: str,
        sid: str,
        appkey: str,
        app_secret: str,
        compress_response_body: bool = True
    ) -> Dict[str, Any]:
        """
        构建公共请求参数

        Args:
            method: 接口方法名
            sid: 卖家账号
            appkey: 应用公钥
            app_secret: 应用私钥
            compress_response_body: 是否启用响应压缩

        Returns:
            公共请求参数
        """
        params = {
            'method': method,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'format': 'json',
            'appkey': appkey,
            'sign_method': 'md5',
            'sid': sid
        }

        # 添加压缩参数
        if compress_response_body:
            params['compress_response_body'] = '1'

        return params
    
    @staticmethod
    def calculate_signature(
        public_params: Dict[str, Any],
        request_body: str,
        app_secret: str
    ) -> str:
        """
        计算签名
        
        Args:
            public_params: 公共参数
            request_body: 请求体JSON字符串
            app_secret: 应用私钥
            
        Returns:
            MD5签名（大写）
        """
        # 第一步：按字典序排序公共参数
        sorted_params = sorted(public_params.items())
        
        # 第二步：拼接排序后的公共参数的key、value
        param_str = ""
        for key, value in sorted_params:
            if value is not None and value != "":
                param_str += f"{key}{value}"
        
        # 第三步：在参数字符串后面拼接请求体
        sign_str = param_str + request_body
        
        # 第四步：在首尾拼接app_secret
        sign_str = app_secret + sign_str + app_secret
        
        # 第五步：生成32位MD5大写签名值
        md5_hash = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
        return md5_hash.upper()

class WDTPostClient:
    """旺店通WMS API POST客户端"""
    
    def __init__(self):
        """初始化客户端"""
        self.config = WDTConfig
        self.logger = logging.getLogger(__name__)
        self.timeout = self.config.REQUEST_TIMEOUT
        self.base_url = self.config.API_URL
        
    def call_api(self, method: str, business_params: Dict[str, Any], compress_response: bool = True) -> Dict[str, Any]:
        """
        调用API

        Args:
            method: 接口方法名（如 'sales.trade.query'）
            business_params: 业务参数
            compress_response: 是否启用响应压缩

        Returns:
            API响应数据

        Raises:
            WDTPostAPIException: API调用异常
        """
        try:
            # 构建公共参数
            public_params = WDTPostSignature.build_request_params(
                method=method,
                sid=self.config.SID,
                appkey=self.config.APP_KEY,
                app_secret=self.config.APP_SECRET,
                compress_response_body=compress_response
            )
            
            # 构建请求体
            request_body = json.dumps(business_params, ensure_ascii=False, separators=(',', ':'))
            
            # 计算签名
            signature = WDTPostSignature.calculate_signature(
                public_params=public_params,
                request_body=request_body,
                app_secret=self.config.APP_SECRET
            )
            
            # 添加签名到公共参数
            public_params['sign'] = signature
            
            # 构建URL（公共参数拼接到URL后）
            url_params = []
            for key, value in public_params.items():
                url_params.append(f"{key}={value}")
            
            full_url = f"{self.base_url}?{'&'.join(url_params)}"
            
            # 记录请求信息（隐藏敏感信息）
            log_params = public_params.copy()
            log_params['sign'] = '***'
            self.logger.debug(f"POST请求URL: {self.base_url}")
            self.logger.debug(f"公共参数: {json.dumps(log_params, ensure_ascii=False, indent=2)}")
            self.logger.debug(f"请求体: {request_body}")
            
            # 发送POST请求
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'WDT-POST-Client/1.0'
            }
            
            response = requests.post(
                full_url,
                data=request_body,
                headers=headers,
                timeout=self.timeout
            )
            
            self.logger.debug(f"HTTP状态码: {response.status_code}")
            self.logger.debug(f"响应头: {dict(response.headers)}")
            
            # 检查HTTP状态
            if response.status_code != 200:
                raise WDTPostAPIException(f"HTTP请求失败: {response.status_code}")
            
            # 解析响应
            try:
                response_text = response.text
                self.logger.debug(f"原始响应长度: {len(response_text)} 字符")

                # 检查是否需要解压缩
                if compress_response and len(response_text) > 0:
                    try:
                        # 尝试解压缩响应
                        decompressed_text = self._decompress_response(response_text)
                        if decompressed_text:
                            response_text = decompressed_text
                            self.logger.debug(f"解压缩成功，解压后长度: {len(response_text)} 字符")
                    except Exception as decompress_error:
                        self.logger.debug(f"解压缩失败，使用原始响应: {decompress_error}")

                self.logger.debug(f"最终响应: {response_text}")

                response_data = json.loads(response_text)
                self.logger.debug(f"解析后响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")

            except json.JSONDecodeError as e:
                self.logger.error(f"JSON解析失败，原始响应: {response_text[:1000]}")
                raise WDTPostAPIException(f"响应JSON解析失败: {e}")
            
            # 检查业务状态
            if response_data.get('flag') != 'success':
                error_code = response_data.get('code', 'unknown')
                error_message = response_data.get('message', '未知错误')
                raise WDTPostAPIException(f"API Error {error_code}: {error_message}")
            
            return response_data
            
        except requests.RequestException as e:
            self.logger.error(f"HTTP请求异常: {e}")
            raise WDTPostAPIException(f"HTTP请求失败: {e}")
        except Exception as e:
            self.logger.error(f"API调用异常: {e}")
            raise WDTPostAPIException(f"API调用失败: {e}")

    def _decompress_response(self, response_text: str) -> Optional[str]:
        """
        解压缩响应数据

        Args:
            response_text: 原始响应文本

        Returns:
            解压缩后的文本，如果解压失败返回None
        """
        try:
            import base64
            import zlib

            # 尝试base64解码
            try:
                compressed_data = base64.b64decode(response_text)
            except:
                # 如果不是base64编码，可能是直接的压缩数据或普通文本
                return None

            # 尝试zlib解压缩
            try:
                decompressed_data = zlib.decompress(compressed_data)
                return decompressed_data.decode('utf-8')
            except:
                pass

            # 尝试gzip解压缩
            try:
                import gzip
                decompressed_data = gzip.decompress(compressed_data)
                return decompressed_data.decode('utf-8')
            except:
                pass

            return None

        except Exception as e:
            self.logger.debug(f"解压缩异常: {e}")
            return None
    
    def query_sales_trades(
        self,
        start_time: str,
        end_time: str,
        trade_status: int,
        page_no: int = 0,
        page_size: int = 10,
        owner_no: Optional[str] = None,
        warehouse_no: Optional[str] = None,
        is_exist_flag: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        查询销售订单
        
        Args:
            start_time: 开始时间 (YYYY-MM-DD HH:MM:SS)
            end_time: 结束时间 (YYYY-MM-DD HH:MM:SS)
            trade_status: 订单状态 (5:已取消, 30:待审核)
            page_no: 页号，从0开始
            page_size: 分页大小，最大100
            owner_no: 货主编号（可选）
            warehouse_no: 仓库编号（可选）
            is_exist_flag: 存在标记（可选，0:全部, 1:存在, 2:不存在）
            
        Returns:
            查询结果
        """
        # 构建业务参数
        params = {
            'start_time': start_time,
            'end_time': end_time,
            'trade_status': trade_status,
            'page_no': page_no,
            'page_size': page_size
        }
        
        # 添加可选参数
        if owner_no is not None:
            params['owner_no'] = owner_no
        if warehouse_no is not None:
            params['warehouse_no'] = warehouse_no
        if is_exist_flag is not None:
            params['is_exist_flag'] = is_exist_flag
        
        return self.call_api('sales.trade.query', params)
    
    def query_stockouts(
        self,
        start_consign_time: str,
        end_consign_time: str,
        page_no: int = 0,
        page_size: int = 100,
        status: Optional[int] = None,
        owner_no: Optional[str] = None,
        warehouse_no: Optional[str] = None,
        stockout_no: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        查询销售出库单

        Args:
            start_consign_time: 开始发货时间 (YYYY-MM-DD HH:MM:SS)
            end_consign_time: 结束发货时间 (YYYY-MM-DD HH:MM:SS)
            page_no: 页号，从0开始
            page_size: 分页大小，最大100
            status: 出库单状态（可选，5:已取消）
            owner_no: 货主编号（可选）
            warehouse_no: 仓库编号（可选）
            stockout_no: 出库单号（可选）

        Returns:
            查询结果
        """
        # 构建业务参数
        params = {
            'start_consign_time': start_consign_time,
            'end_consign_time': end_consign_time,
            'page_no': page_no,
            'page_size': page_size
        }

        # 添加可选参数
        if status is not None:
            params['status'] = status
        if owner_no is not None:
            params['owner_no'] = owner_no
        if warehouse_no is not None:
            params['warehouse_no'] = warehouse_no
        if stockout_no is not None:
            params['stockout_no'] = stockout_no

        return self.call_api('stockout.query', params)

    def test_connection(self) -> bool:
        """
        测试连接

        Returns:
            连接是否正常
        """
        try:
            # 使用一个简单的查询来测试连接
            from datetime import datetime, timedelta

            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)

            self.query_sales_trades(
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                trade_status=30,
                page_no=0,
                page_size=1
            )
            return True
        except Exception as e:
            self.logger.error(f"连接测试失败: {e}")
            return False

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试获取已取消的销售订单（使用 sales.trade.query 接口）
"""

import logging
from datetime import datetime, timedelta
from get_canceled_sales import setup_logging, get_today_time_range
from wdt_post_client import WDTPostClient

def test_sales_trade_query():
    """测试 sales.trade.query 接口查询已取消订单"""
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        # 获取当天时间范围
        start_time, end_time = get_today_time_range()
        logger.info(f"查询时间范围: {start_time} 至 {end_time}")
        
        # 测试不同的状态码
        status_codes_to_test = [5, 30, 40, 50, 60, 70, 80, 90, 100]
        
        for status_code in status_codes_to_test:
            try:
                logger.info(f"\n=== 测试销售订单状态码 {status_code} ===")
                
                result = client.query_sales_trades(
                    start_time=start_time,
                    end_time=end_time,
                    trade_status=status_code,
                    page_no=0,
                    page_size=5  # 只查询5条测试
                )
                
                if isinstance(result, dict) and result.get('flag') == 'success':
                    # 解析数据
                    trades = []
                    if 'content' in result:
                        content = result.get('content', [])
                        if isinstance(content, list):
                            trades = content
                    
                    count = len(trades)
                    total = result.get('total', 0)
                    
                    logger.info(f"状态码 {status_code}: 查询到 {count} 条记录，总计 {total} 条")
                    
                    if count > 0:
                        # 显示第一条记录的详细信息
                        first_trade = trades[0]
                        trade_no = first_trade.get('trade_no', '未知')
                        trade_status = first_trade.get('trade_status', '未知')
                        trade_status_name = first_trade.get('trade_status_name', '未知')
                        logistics_no = first_trade.get('logistics_no', '')
                        trade_time = first_trade.get('trade_time', '')
                        modified = first_trade.get('modified', '')
                        
                        logger.info(f"  示例记录:")
                        logger.info(f"    订单号: {trade_no}")
                        logger.info(f"    状态: {trade_status}")
                        logger.info(f"    状态名称: {trade_status_name}")
                        logger.info(f"    物流单号: {logistics_no}")
                        logger.info(f"    下单时间: {trade_time}")
                        logger.info(f"    修改时间: {modified}")
                        
                        # 显示所有字段名
                        logger.debug(f"    所有字段: {list(first_trade.keys())}")
                        
                        # 如果是状态5，显示更多详情
                        if status_code == 5:
                            logger.info(f"  === 状态5的详细分析 ===")
                            for i, trade in enumerate(trades[:3], 1):
                                logger.info(f"  记录{i}: 订单号={trade.get('trade_no', '未知')}, "
                                          f"状态={trade.get('trade_status', '未知')}, "
                                          f"物流单号={trade.get('logistics_no', '无')}")
                else:
                    logger.warning(f"状态码 {status_code}: API调用失败或无数据")
                    if isinstance(result, dict):
                        logger.warning(f"错误信息: {result.get('message', '未知错误')}")
                    
            except Exception as e:
                logger.error(f"状态码 {status_code} 测试失败: {e}")
                
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)
        return False

def test_no_status_filter():
    """测试不使用状态过滤，查看所有销售订单的状态"""
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        # 获取当天时间范围
        start_time, end_time = get_today_time_range()
        
        logger.info(f"\n=== 测试不使用状态过滤（销售订单） ===")
        
        # 不设置trade_status参数，查看所有订单
        result = client.query_sales_trades(
            start_time=start_time,
            end_time=end_time,
            page_no=0,
            page_size=20  # 查询20条
        )
        
        if isinstance(result, dict) and result.get('flag') == 'success':
            # 解析数据
            trades = []
            if 'content' in result:
                content = result.get('content', [])
                if isinstance(content, list):
                    trades = content
            
            count = len(trades)
            total = result.get('total', 0)
            
            logger.info(f"不使用状态过滤: 查询到 {count} 条记录，总计 {total} 条")
            
            # 统计不同状态的订单
            status_count = {}
            for trade in trades:
                status = trade.get('trade_status', '未知')
                status_name = trade.get('trade_status_name', '未知')
                status_key = f"{status}({status_name})"
                
                if status_key not in status_count:
                    status_count[status_key] = 0
                status_count[status_key] += 1
            
            logger.info(f"状态统计:")
            for status_key, count in status_count.items():
                logger.info(f"  {status_key}: {count} 条")
            
            # 显示前几条记录的详细信息
            logger.info(f"\n前5条记录详情:")
            for i, trade in enumerate(trades[:5], 1):
                trade_no = trade.get('trade_no', '未知')
                status = trade.get('trade_status', '未知')
                status_name = trade.get('trade_status_name', '未知')
                logistics_no = trade.get('logistics_no', '')
                trade_time = trade.get('trade_time', '')
                modified = trade.get('modified', '')
                
                logger.info(f"  {i}. 订单号: {trade_no}")
                logger.info(f"     状态: {status}({status_name})")
                logger.info(f"     物流单号: {logistics_no}")
                logger.info(f"     下单时间: {trade_time}")
                logger.info(f"     修改时间: {modified}")
                logger.info("")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)
        return False

def main():
    """主测试函数"""
    logger = setup_logging()
    
    logger.info("=== 开始测试销售订单查询接口 ===")
    
    # 测试1: 不使用状态过滤，查看所有状态
    logger.info("\n1. 测试不使用状态过滤...")
    if test_no_status_filter():
        logger.info("✅ 无状态过滤测试完成")
    else:
        logger.error("❌ 无状态过滤测试失败")
    
    # 测试2: 测试不同状态码
    logger.info("\n2. 测试不同状态码...")
    if test_sales_trade_query():
        logger.info("✅ 状态码测试完成")
    else:
        logger.error("❌ 状态码测试失败")
    
    logger.info("\n=== 调试完成 ===")
    logger.info("请查看日志输出，确认状态码5是否为已取消状态")
    
    return 0

if __name__ == '__main__':
    exit(main())

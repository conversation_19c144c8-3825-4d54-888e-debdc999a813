#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试出库单字段，查看所有可用字段和可能的取消标识
"""

import logging
from datetime import datetime, timedelta
from get_canceled_sales import setup_logging, get_today_time_range
from wdt_post_client import WDTPostClient
import json

def analyze_stockout_fields():
    """分析出库单的所有字段"""
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        # 获取当天时间范围
        start_time, end_time = get_today_time_range()
        logger.info(f"查询时间范围: {start_time} 至 {end_time}")
        
        # 查询出库单（不使用状态过滤）
        result = client.query_stockouts(
            start_consign_time=start_time,
            end_consign_time=end_time,
            page_no=0,
            page_size=10  # 查询10条
        )
        
        if isinstance(result, dict) and result.get('flag') == 'success':
            # 解析数据
            stockouts = []
            if 'content' in result:
                content = result.get('content', [])
                if isinstance(content, list):
                    stockouts = content
            
            if stockouts:
                logger.info(f"查询到 {len(stockouts)} 条出库单")
                
                # 分析第一条记录的所有字段
                first_stockout = stockouts[0]
                logger.info(f"\n=== 第一条出库单的所有字段 ===")
                for key, value in first_stockout.items():
                    logger.info(f"{key}: {value}")
                
                # 查找可能与取消相关的字段
                logger.info(f"\n=== 可能与取消相关的字段 ===")
                cancel_related_fields = []
                for key, value in first_stockout.items():
                    key_lower = key.lower()
                    if any(keyword in key_lower for keyword in ['cancel', 'status', 'state', 'flag', 'deleted', 'invalid']):
                        cancel_related_fields.append((key, value))
                        logger.info(f"{key}: {value}")
                
                if not cancel_related_fields:
                    logger.info("未找到明显的取消相关字段")
                
                # 分析所有记录的状态分布
                logger.info(f"\n=== 所有记录的状态分析 ===")
                status_distribution = {}
                for stockout in stockouts:
                    status = stockout.get('status', '未知')
                    status_name = stockout.get('status_name', '未知')
                    is_deleted = stockout.get('is_deleted', '未知')
                    
                    key = f"status:{status}, status_name:{status_name}, is_deleted:{is_deleted}"
                    if key not in status_distribution:
                        status_distribution[key] = 0
                    status_distribution[key] += 1
                
                for status_key, count in status_distribution.items():
                    logger.info(f"{status_key} - {count} 条")
                
                # 检查是否有时间相关的取消标识
                logger.info(f"\n=== 时间字段分析 ===")
                for i, stockout in enumerate(stockouts[:3], 1):
                    logger.info(f"记录 {i}:")
                    time_fields = ['created', 'modified', 'consign_time', 'cancel_time', 'delete_time']
                    for field in time_fields:
                        if field in stockout:
                            logger.info(f"  {field}: {stockout.get(field)}")
                    logger.info("")
        
        return True
        
    except Exception as e:
        logger.error(f"分析失败: {e}", exc_info=True)
        return False

def test_different_time_ranges():
    """测试不同的时间范围，看是否能找到已取消的出库单"""
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        # 测试过去几天的数据
        now = datetime.now()
        
        time_ranges = [
            ("今天", now.replace(hour=0, minute=0, second=0), now),
            ("昨天", now - timedelta(days=1), now - timedelta(days=1) + timedelta(hours=23, minutes=59, seconds=59)),
            ("前天", now - timedelta(days=2), now - timedelta(days=2) + timedelta(hours=23, minutes=59, seconds=59)),
            ("过去7天", now - timedelta(days=7), now),
        ]
        
        for period_name, start_time, end_time in time_ranges:
            logger.info(f"\n=== 测试时间范围: {period_name} ===")
            logger.info(f"时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} 至 {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            try:
                result = client.query_stockouts(
                    start_consign_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                    end_consign_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                    page_no=0,
                    page_size=5
                )
                
                if isinstance(result, dict) and result.get('flag') == 'success':
                    stockouts = []
                    if 'content' in result:
                        content = result.get('content', [])
                        if isinstance(content, list):
                            stockouts = content
                    
                    total = result.get('total', 0)
                    logger.info(f"查询到 {len(stockouts)} 条记录，总计 {total} 条")
                    
                    if stockouts:
                        # 分析状态分布
                        status_count = {}
                        for stockout in stockouts:
                            status = stockout.get('status', '未知')
                            if status not in status_count:
                                status_count[status] = 0
                            status_count[status] += 1
                        
                        logger.info(f"状态分布: {status_count}")
                else:
                    logger.warning(f"{period_name}: API调用失败")
                    
            except Exception as e:
                logger.error(f"{period_name} 测试失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"时间范围测试失败: {e}", exc_info=True)
        return False

def main():
    """主测试函数"""
    logger = setup_logging()
    
    logger.info("=== 开始调试出库单字段和取消标识 ===")
    
    # 测试1: 分析出库单字段
    logger.info("\n1. 分析出库单字段...")
    if analyze_stockout_fields():
        logger.info("✅ 字段分析完成")
    else:
        logger.error("❌ 字段分析失败")
    
    # 测试2: 测试不同时间范围
    logger.info("\n2. 测试不同时间范围...")
    if test_different_time_ranges():
        logger.info("✅ 时间范围测试完成")
    else:
        logger.error("❌ 时间范围测试失败")
    
    logger.info("\n=== 调试完成 ===")
    logger.info("请查看日志输出，寻找可能的取消标识字段")
    
    return 0

if __name__ == '__main__':
    exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
模拟测试获取当天已取消的销售出库明细包含物流单号
"""

import logging
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from get_canceled_sales import setup_logging, query_canceled_sales, get_today_time_range, export_to_excel

def create_mock_stockout_data():
    """创建模拟的出库单数据"""
    return [
        {
            'stockout_no': 'SO2025072201',
            'trade_no': 'T2025072201',
            'logistics_no': 'SF1234567890',  # 有物流单号
            'shop_name': '测试店铺1',
            'receiver_name': '张三',
            'receiver_mobile': '13800138001',
            'receiver_province': '广东省',
            'receiver_city': '深圳市',
            'receiver_district': '南山区',
            'receiver_address': '科技园南区',
            'consign_time': '2025-07-22 10:00:00',
            'created': '2025-07-22 09:00:00',
            'modified': '2025-07-22 11:00:00',
            'status': 5,
            'remark': '客户要求取消'
        },
        {
            'stockout_no': 'SO2025072202',
            'trade_no': 'T2025072202',
            'logistics_no': '',  # 无物流单号
            'shop_name': '测试店铺2',
            'receiver_name': '李四',
            'receiver_mobile': '13800138002',
            'receiver_province': '北京市',
            'receiver_city': '北京市',
            'receiver_district': '朝阳区',
            'receiver_address': '建国门外大街',
            'consign_time': '2025-07-22 14:00:00',
            'created': '2025-07-22 13:00:00',
            'modified': '2025-07-22 14:30:00',
            'status': 5,
            'remark': '库存不足取消'
        },
        {
            'stockout_no': 'SO2025072203',
            'trade_no': 'T2025072203',
            'logistics_no': 'YTO9876543210',  # 有物流单号
            'shop_name': '测试店铺3',
            'receiver_name': '王五',
            'receiver_mobile': '13800138003',
            'receiver_province': '上海市',
            'receiver_city': '上海市',
            'receiver_district': '浦东新区',
            'receiver_address': '陆家嘴金融区',
            'consign_time': '2025-07-22 16:00:00',
            'created': '2025-07-22 15:00:00',
            'modified': '2025-07-22 16:30:00',
            'status': 5,
            'remark': '地址错误取消'
        }
    ]

def create_mock_api_response(stockouts_data):
    """创建模拟的API响应"""
    return {
        'flag': 'success',
        'code': '0',
        'message': 'success',
        'content': stockouts_data,
        'total': len(stockouts_data)
    }

def test_mock_query_canceled_sales():
    """测试查询已取消销售出库单的逻辑"""
    logger = setup_logging()
    
    try:
        # 创建模拟数据
        mock_stockouts = create_mock_stockout_data()
        mock_response = create_mock_api_response(mock_stockouts)
        
        # 创建模拟客户端
        mock_client = Mock()
        mock_client.query_stockouts.return_value = mock_response
        
        # 获取时间范围
        start_time, end_time = get_today_time_range()
        
        logger.info(f"开始模拟测试查询已取消销售出库单...")
        logger.info(f"模拟数据包含 {len(mock_stockouts)} 条出库单")
        
        # 执行查询
        all_stockouts, stockouts_with_logistics = query_canceled_sales(mock_client, start_time, end_time)
        
        # 验证结果
        logger.info(f"\n=== 查询结果验证 ===")
        logger.info(f"总计已取消出库单: {len(all_stockouts)} 条")
        logger.info(f"包含物流单号的: {len(stockouts_with_logistics)} 条")
        logger.info(f"无物流单号的: {len(all_stockouts) - len(stockouts_with_logistics)} 条")
        
        # 验证预期结果
        expected_total = 3
        expected_with_logistics = 2  # SO2025072201 和 SO2025072203 有物流单号
        expected_without_logistics = 1  # SO2025072202 无物流单号
        
        assert len(all_stockouts) == expected_total, f"总数不匹配: 期望{expected_total}, 实际{len(all_stockouts)}"
        assert len(stockouts_with_logistics) == expected_with_logistics, f"有物流单号数量不匹配: 期望{expected_with_logistics}, 实际{len(stockouts_with_logistics)}"
        
        logger.info("✅ 查询结果验证通过")
        
        # 显示详细信息
        logger.info(f"\n=== 包含物流单号的出库单详情 ===")
        for i, stockout in enumerate(stockouts_with_logistics, 1):
            stockout_no = stockout.get('stockout_no', '未知')
            logistics_no = stockout.get('logistics_no', '')
            trade_no = stockout.get('trade_no', '')
            shop_name = stockout.get('shop_name', '')
            logger.info(f"  {i}. 出库单号: {stockout_no}")
            logger.info(f"     物流单号: {logistics_no}")
            logger.info(f"     订单号: {trade_no}")
            logger.info(f"     店铺: {shop_name}")
            logger.info("")
        
        # 验证API调用参数
        mock_client.query_stockouts.assert_called_with(
            start_consign_time=start_time,
            end_consign_time=end_time,
            start_modified=start_time,
            end_modified=end_time,
            status=5,
            page_no=0,
            page_size=100
        )
        logger.info("✅ API调用参数验证通过")
        
        return all_stockouts, stockouts_with_logistics
        
    except Exception as e:
        logger.error(f"模拟测试失败: {e}", exc_info=True)
        return None, None

def test_mock_export_excel():
    """测试Excel导出功能"""
    logger = setup_logging()
    
    try:
        # 创建模拟数据
        mock_stockouts = create_mock_stockout_data()
        
        logger.info(f"测试Excel导出功能...")
        
        # 测试导出所有数据
        logger.info("测试导出所有已取消出库单...")
        if export_to_excel(mock_stockouts, "测试_所有已取消销售出库单"):
            logger.info("✅ 所有数据导出测试通过")
        else:
            logger.error("❌ 所有数据导出测试失败")
            return False
        
        # 测试导出包含物流单号的数据
        stockouts_with_logistics = [s for s in mock_stockouts if s.get('logistics_no', '').strip()]
        logger.info("测试导出包含物流单号的出库单...")
        if export_to_excel(stockouts_with_logistics, "测试_包含物流单号的已取消销售出库单"):
            logger.info("✅ 包含物流单号数据导出测试通过")
        else:
            logger.error("❌ 包含物流单号数据导出测试失败")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Excel导出测试失败: {e}", exc_info=True)
        return False

def main():
    """主测试函数"""
    logger = setup_logging()
    
    logger.info("=== 开始模拟测试获取当天已取消的销售出库明细包含物流单号 ===")
    
    # 测试1: 查询功能
    logger.info("\n1. 测试查询功能...")
    all_stockouts, stockouts_with_logistics = test_mock_query_canceled_sales()
    if all_stockouts is not None:
        logger.info("✅ 查询功能测试通过")
    else:
        logger.error("❌ 查询功能测试失败")
        return 1
    
    # 测试2: Excel导出功能
    logger.info("\n2. 测试Excel导出功能...")
    if test_mock_export_excel():
        logger.info("✅ Excel导出功能测试通过")
    else:
        logger.error("❌ Excel导出功能测试失败")
        return 1
    
    logger.info("\n=== 所有模拟测试完成 ===")
    logger.info("代码逻辑验证通过，可以在实际环境中使用")
    
    return 0

if __name__ == '__main__':
    exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试扩展物流单号查询功能
"""

import logging
from datetime import datetime, timedelta
from get_canceled_sales import setup_logging, query_canceled_sales_extended
from wdt_post_client import WDTPostClient

def test_extended_query():
    """测试扩展查询功能"""
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        logger.info("测试扩展查询功能（查询过去3天）...")
        
        # 执行扩展查询
        all_trades, trades_with_logistics = query_canceled_sales_extended(client, 3)
        
        logger.info(f"\n=== 扩展查询结果 ===")
        logger.info(f"总计已取消订单: {len(all_trades)} 条")
        logger.info(f"包含物流信息的: {len(trades_with_logistics)} 条")
        
        # 显示包含物流信息的订单详情
        if trades_with_logistics:
            logger.info(f"\n包含物流信息的订单详情:")
            for i, trade in enumerate(trades_with_logistics, 1):
                trade_no = trade.get('trade_no', '未知')
                trade_time = trade.get('trade_time', '未知')
                shop_name = trade.get('shop_name', '未知')
                
                # 检查所有物流字段
                logistics_fields = [
                    'logistics_no', 'logistics_code', 'logistics_name', 
                    'express_no', 'tracking_no', 'waybill_no', 'delivery_no'
                ]
                
                logistics_info = {}
                for field in logistics_fields:
                    value = trade.get(field, '')
                    if value and str(value).strip():
                        logistics_info[field] = str(value).strip()
                
                logger.info(f"  {i}. 订单号: {trade_no}")
                logger.info(f"     下单时间: {trade_time}")
                logger.info(f"     店铺: {shop_name}")
                logger.info(f"     物流信息: {logistics_info}")
                logger.info("")
        else:
            logger.info("在过去3天中没有找到包含物流信息的已取消订单")
            
            # 显示一些样本订单的详细字段
            if all_trades:
                logger.info(f"\n=== 样本订单字段分析 ===")
                sample_trade = all_trades[0]
                logger.info(f"第一条订单的所有字段:")
                for key, value in sample_trade.items():
                    logger.info(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        logger.error(f"扩展查询测试失败: {e}", exc_info=True)
        return False

def test_single_day_detailed():
    """测试单天详细查询"""
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        # 查询昨天的数据
        yesterday = datetime.now() - timedelta(days=1)
        start_time = yesterday.replace(hour=0, minute=0, second=0).strftime('%Y-%m-%d %H:%M:%S')
        end_time = yesterday.replace(hour=23, minute=59, second=59).strftime('%Y-%m-%d %H:%M:%S')
        
        logger.info(f"详细查询昨天的数据: {yesterday.strftime('%Y-%m-%d')}")
        
        result = client.query_sales_trades(
            start_time=start_time,
            end_time=end_time,
            trade_status=5,
            page_no=0,
            page_size=100
        )
        
        if isinstance(result, dict) and result.get('flag') == 'success':
            trades = result.get('content', [])
            total = result.get('total', 0)
            
            logger.info(f"查询到 {len(trades)} 条记录，总计 {total} 条")
            
            # 详细分析每条记录
            logistics_found = 0
            for i, trade in enumerate(trades, 1):
                trade_no = trade.get('trade_no', '未知')
                
                # 检查所有可能的物流字段
                logistics_fields = [
                    'logistics_no', 'logistics_code', 'logistics_name', 
                    'express_no', 'tracking_no', 'waybill_no', 'delivery_no',
                    'logistics_company', 'express_company'
                ]
                
                found_logistics = {}
                for field in logistics_fields:
                    value = trade.get(field, '')
                    if value and str(value).strip():
                        found_logistics[field] = str(value).strip()
                
                if found_logistics:
                    logistics_found += 1
                    logger.info(f"✓ 订单 {trade_no} 包含物流信息: {found_logistics}")
                
                # 显示前5条的详细信息
                if i <= 5:
                    logger.info(f"\n订单 {i} 详细信息:")
                    logger.info(f"  订单号: {trade_no}")
                    logger.info(f"  状态: {trade.get('trade_status', '未知')}")
                    logger.info(f"  下单时间: {trade.get('trade_time', '未知')}")
                    logger.info(f"  修改时间: {trade.get('modified', '未知')}")
                    
                    # 显示所有物流相关字段
                    for field in logistics_fields:
                        value = trade.get(field, '')
                        logger.info(f"  {field}: '{value}'")
            
            logger.info(f"\n昨天总结: 在 {len(trades)} 条已取消订单中，发现 {logistics_found} 条包含物流信息")
        
        return True
        
    except Exception as e:
        logger.error(f"单天详细查询失败: {e}", exc_info=True)
        return False

def main():
    """主测试函数"""
    logger = setup_logging()
    
    logger.info("=== 开始测试扩展物流单号查询功能 ===")
    
    # 测试1: 单天详细查询
    logger.info("\n1. 测试单天详细查询...")
    if test_single_day_detailed():
        logger.info("✅ 单天详细查询完成")
    else:
        logger.error("❌ 单天详细查询失败")
    
    # 测试2: 扩展查询
    logger.info("\n2. 测试扩展查询...")
    if test_extended_query():
        logger.info("✅ 扩展查询完成")
    else:
        logger.error("❌ 扩展查询失败")
    
    logger.info("\n=== 测试完成 ===")
    
    return 0

if __name__ == '__main__':
    exit(main())

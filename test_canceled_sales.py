#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试获取当天已取消的销售出库明细包含物流单号
"""

import logging
from datetime import datetime, timedelta
from get_canceled_sales import setup_logging, query_canceled_sales, get_today_time_range
from wdt_post_client import WDTPostClient

def test_stockout_query():
    """测试 stockout.query 接口"""
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        # 测试连接
        logger.info("测试API连接...")
        if not client.test_connection():
            logger.error("API连接测试失败")
            return False
        
        logger.info("API连接测试成功")
        
        # 获取当天时间范围
        start_time, end_time = get_today_time_range()
        logger.info(f"查询时间范围: {start_time} 至 {end_time}")
        
        # 测试查询已取消的销售出库单
        logger.info("开始测试查询已取消的销售出库单...")
        
        # 先测试一个小的查询
        test_result = client.query_stockouts(
            start_consign_time=start_time,
            end_consign_time=end_time,
            start_modified=start_time,
            end_modified=end_time,
            status=5,  # 已取消状态
            page_no=0,
            page_size=10  # 只查询10条测试
        )
        
        logger.info(f"测试查询结果: {test_result}")
        
        # 检查响应结构
        if isinstance(test_result, dict):
            logger.info(f"响应字段: {list(test_result.keys())}")
            
            # 检查是否有数据
            data_found = False
            if 'content' in test_result:
                content = test_result.get('content', [])
                if isinstance(content, list) and len(content) > 0:
                    data_found = True
                    logger.info(f"在content字段中找到 {len(content)} 条数据")
                    # 显示第一条数据的结构
                    if content:
                        first_item = content[0]
                        logger.info(f"第一条数据字段: {list(first_item.keys()) if isinstance(first_item, dict) else type(first_item)}")
                        if isinstance(first_item, dict):
                            logistics_no = first_item.get('logistics_no', '')
                            stockout_no = first_item.get('stockout_no', '')
                            logger.info(f"出库单号: {stockout_no}, 物流单号: {logistics_no}")
            
            if 'data' in test_result:
                data = test_result.get('data', [])
                if isinstance(data, list) and len(data) > 0:
                    data_found = True
                    logger.info(f"在data字段中找到 {len(data)} 条数据")
            
            if not data_found:
                logger.warning("测试查询没有返回数据，这可能是正常的（当天没有已取消的出库单）")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)
        return False

def test_full_query():
    """测试完整的查询功能"""
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        # 获取当天时间范围
        start_time, end_time = get_today_time_range()
        
        # 执行完整查询
        logger.info("执行完整查询测试...")
        all_stockouts, stockouts_with_logistics = query_canceled_sales(client, start_time, end_time)
        
        logger.info(f"查询结果:")
        logger.info(f"  总计已取消出库单: {len(all_stockouts)} 条")
        logger.info(f"  包含物流单号的: {len(stockouts_with_logistics)} 条")
        logger.info(f"  无物流单号的: {len(all_stockouts) - len(stockouts_with_logistics)} 条")
        
        # 显示包含物流单号的出库单详情
        if stockouts_with_logistics:
            logger.info("\n包含物流单号的出库单详情:")
            for i, stockout in enumerate(stockouts_with_logistics[:5], 1):  # 只显示前5条
                stockout_no = stockout.get('stockout_no', '未知')
                logistics_no = stockout.get('logistics_no', '')
                trade_no = stockout.get('trade_no', '')
                shop_name = stockout.get('shop_name', '')
                logger.info(f"  {i}. 出库单号: {stockout_no}")
                logger.info(f"     物流单号: {logistics_no}")
                if trade_no:
                    logger.info(f"     订单号: {trade_no}")
                if shop_name:
                    logger.info(f"     店铺: {shop_name}")
                logger.info("")
        
        return True
        
    except Exception as e:
        logger.error(f"完整查询测试失败: {e}", exc_info=True)
        return False

def main():
    """主测试函数"""
    logger = setup_logging()
    
    logger.info("=== 开始测试获取当天已取消的销售出库明细包含物流单号 ===")
    
    # 测试1: 基础API查询
    logger.info("\n1. 测试基础API查询...")
    if test_stockout_query():
        logger.info("✅ 基础API查询测试通过")
    else:
        logger.error("❌ 基础API查询测试失败")
        return 1
    
    # 测试2: 完整查询功能
    logger.info("\n2. 测试完整查询功能...")
    if test_full_query():
        logger.info("✅ 完整查询功能测试通过")
    else:
        logger.error("❌ 完整查询功能测试失败")
        return 1
    
    logger.info("\n=== 所有测试完成 ===")
    return 0

if __name__ == '__main__':
    exit(main())
